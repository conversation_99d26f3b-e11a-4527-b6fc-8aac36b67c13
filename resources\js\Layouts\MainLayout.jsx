import React from "react";

export default function MainLayout({ auth, children }) {
    return (
        <div className="min-h-screen bg-[#f3f2ef] flex flex-col">
            {/* LinkedIn Blue Navbar */}
            <nav className="bg-[#0a66c2] shadow-sm sticky top-0 z-20">
                <div className="max-w-7xl mx-auto px-4 flex items-center h-14">
                    <a href="/" className="flex items-center space-x-2">
                        <svg
                            className="w-8 h-8 text-white"
                            fill="currentColor"
                            viewBox="0 0 24 24"
                        >
                            <circle cx="4.983" cy="5.009" r="2.188" />
                            <path d="M9.237 8.855v10.32H5.927V8.855h3.31zm-1.655-1.66c-1.07 0-1.77.7-1.77 1.62 0 .91.68 1.62 1.73 1.62h.02c1.09 0 1.77-.71 1.77-1.62-.02-.92-.68-1.62-1.75-1.62zM20.452 13.37v5.805h-3.31v-5.42c0-1.36-.49-2.29-1.72-2.29-.94 0-1.5.63-1.75 1.24-.09.22-.11.53-.11.84v5.63h-3.31s.04-9.13 0-10.08h3.31v1.43c.44-.68 1.23-1.65 3-1.65 2.19 0 3.83 1.43 3.83 4.49z" />
                        </svg>
                        <span className="text-2xl font-bold text-white tracking-tight">
                            LinkedIn
                        </span>
                    </a>
                    {/* Center search bar (placeholder) */}
                    <div className="flex-1 flex justify-center">
                        <input
                            type="text"
                            placeholder="Search"
                            className="w-80 px-4 py-1 rounded bg-[#eef3f8] text-gray-900 placeholder-gray-500 focus:outline-none"
                        />
                    </div>
                    {/* Right nav icons (Home, Network, Jobs, Messaging, Notifications, Me) */}
                    <div className="flex items-center space-x-6">
                        <a
                            href="/"
                            className="flex flex-col items-center text-white hover:text-[#dbeafe]"
                        >
                            <svg
                                className="w-6 h-6 mb-1"
                                fill="none"
                                stroke="currentColor"
                                strokeWidth="2"
                                viewBox="0 0 24 24"
                            >
                                <path d="M3 12l2-2m0 0l7-7 7 7M5 10v10a1 1 0 001 1h3m10-11l2 2m-2-2v10a1 1 0 01-1 1h-3m-6 0h6" />
                            </svg>
                            <span className="text-xs">Home</span>
                        </a>
                        <a
                            href="#"
                            className="flex flex-col items-center text-white hover:text-[#dbeafe]"
                        >
                            <svg
                                className="w-6 h-6 mb-1"
                                fill="none"
                                stroke="currentColor"
                                strokeWidth="2"
                                viewBox="0 0 24 24"
                            >
                                <path d="M17 20h5v-2a4 4 0 00-3-3.87M9 20H4v-2a4 4 0 013-3.87M16 3.13a4 4 0 010 7.75M8 3.13a4 4 0 000 7.75" />
                            </svg>
                            <span className="text-xs">Network</span>
                        </a>
                        <a
                            href="#"
                            className="flex flex-col items-center text-white hover:text-[#dbeafe]"
                        >
                            <svg
                                className="w-6 h-6 mb-1"
                                fill="none"
                                stroke="currentColor"
                                strokeWidth="2"
                                viewBox="0 0 24 24"
                            >
                                <path d="M17 9V7a5 5 0 00-10 0v2a2 2 0 00-2 2v7a2 2 0 002 2h10a2 2 0 002-2v-7a2 2 0 00-2-2z" />
                            </svg>
                            <span className="text-xs">Jobs</span>
                        </a>
                        <a
                            href="#"
                            className="flex flex-col items-center text-white hover:text-[#dbeafe]"
                        >
                            <svg
                                className="w-6 h-6 mb-1"
                                fill="none"
                                stroke="currentColor"
                                strokeWidth="2"
                                viewBox="0 0 24 24"
                            >
                                <path d="M21 15a2 2 0 01-2 2H5a2 2 0 01-2-2V7a2 2 0 012-2h14a2 2 0 012 2z" />
                            </svg>
                            <span className="text-xs">Messaging</span>
                        </a>
                        <a
                            href="#"
                            className="flex flex-col items-center text-white hover:text-[#dbeafe]"
                        >
                            <svg
                                className="w-6 h-6 mb-1"
                                fill="none"
                                stroke="currentColor"
                                strokeWidth="2"
                                viewBox="0 0 24 24"
                            >
                                <path d="M15 17h5l-1.405-1.405A2.032 2.032 0 0118 14.158V11a6.002 6.002 0 00-4-5.659V5a2 2 0 10-4 0v.341C7.67 6.165 6 8.388 6 11v3.159c0 .538-.214 1.055-.595 1.436L4 17h5m6 0v1a3 3 0 11-6 0v-1m6 0H9" />
                            </svg>
                            <span className="text-xs">Notifications</span>
                        </a>
                        {auth?.user ? (
                            <a
                                href="/profile"
                                className="flex flex-col items-center text-white hover:text-[#dbeafe]"
                            >
                                <svg
                                    className="w-6 h-6 mb-1"
                                    fill="currentColor"
                                    viewBox="0 0 24 24"
                                >
                                    <circle cx="12" cy="8" r="4" />
                                    <path d="M6 20v-2a4 4 0 014-4h0a4 4 0 014 4v2" />
                                </svg>
                                <span className="text-xs">Me</span>
                            </a>
                        ) : (
                            <>
                                <a
                                    href="/login"
                                    className="flex flex-col items-center text-white hover:text-[#dbeafe]"
                                >
                                    <svg
                                        className="w-6 h-6 mb-1"
                                        fill="none"
                                        stroke="currentColor"
                                        strokeWidth="2"
                                        viewBox="0 0 24 24"
                                    >
                                        <path d="M5 12h14M12 5l7 7-7 7" />
                                    </svg>
                                    <span className="text-xs">Login</span>
                                </a>
                                <a
                                    href="/register"
                                    className="flex flex-col items-center text-white hover:text-[#dbeafe]"
                                >
                                    <svg
                                        className="w-6 h-6 mb-1"
                                        fill="none"
                                        stroke="currentColor"
                                        strokeWidth="2"
                                        viewBox="0 0 24 24"
                                    >
                                        <path d="M12 4v16m8-8H4" />
                                    </svg>
                                    <span className="text-xs">Register</span>
                                </a>
                            </>
                        )}
                    </div>
                </div>
            </nav>
            {/* Main 3-column Layout */}
            <main className="flex-1 max-w-7xl mx-auto w-full flex gap-6 py-8 px-2">
                {/* Left Sidebar */}
                <aside className="hidden lg:block w-1/4 max-w-xs">
                    <div className="bg-white rounded-lg shadow p-4 mb-4">
                        {/* Profile summary placeholder */}
                        {auth?.user ? (
                            <>
                                <div className="flex flex-col items-center">
                                    <div className="w-16 h-16 rounded-full bg-blue-200 flex items-center justify-center text-2xl font-bold text-blue-700 mb-2">
                                        {auth.user.name[0]}
                                    </div>
                                    <div className="font-semibold text-lg">
                                        {auth.user.name}
                                    </div>
                                    <div className="text-gray-500 text-sm">
                                        {auth.user.email}
                                    </div>
                                </div>
                            </>
                        ) : (
                            <div className="text-center text-gray-500">
                                Sign in to see your profile
                            </div>
                        )}
                    </div>
                    <div className="bg-white rounded-lg shadow p-4">
                        <div className="font-semibold text-gray-700 mb-2">
                            Navigation
                        </div>
                        <ul className="space-y-2">
                            <li>
                                <a
                                    href="/"
                                    className="text-blue-700 hover:underline"
                                >
                                    Home
                                </a>
                            </li>
                            <li>
                                <a
                                    href="/profile"
                                    className="text-blue-700 hover:underline"
                                >
                                    Profile
                                </a>
                            </li>
                        </ul>
                    </div>
                </aside>
                {/* Center Feed/Content */}
                <section className="flex-1 min-w-0">{children}</section>
                {/* Right Sidebar */}
                <aside className="hidden lg:block w-1/4 max-w-xs">
                    <div className="bg-white rounded-lg shadow p-4 mb-4">
                        <div className="font-semibold text-gray-700 mb-2">
                            People you may know
                        </div>
                        <ul className="space-y-2">
                            <li className="flex items-center gap-2">
                                <div className="w-8 h-8 rounded-full bg-blue-100 flex items-center justify-center text-blue-700 font-bold">
                                    A
                                </div>{" "}
                                <span className="text-gray-700">Alice</span>
                            </li>
                            <li className="flex items-center gap-2">
                                <div className="w-8 h-8 rounded-full bg-blue-100 flex items-center justify-center text-blue-700 font-bold">
                                    B
                                </div>{" "}
                                <span className="text-gray-700">Bob</span>
                            </li>
                        </ul>
                    </div>
                    <div className="bg-white rounded-lg shadow p-4">
                        <div className="font-semibold text-gray-700 mb-2">
                            Ad
                        </div>
                        <div className="text-gray-500 text-sm">
                            Your ad here
                        </div>
                    </div>
                </aside>
            </main>
            {/* Footer */}
            <footer className="bg-white border-t border-gray-200 text-center py-4 text-sm text-gray-500 mt-8">
                &copy; {new Date().getFullYear()} TinyLinkedIn. Not affiliated
                with LinkedIn.
            </footer>
        </div>
    );
}
