import "./bootstrap";
import "../css/app.css";

import { createRoot } from "react-dom/client";
import { createInertiaApp } from "@inertiajs/react";
import { resolvePageComponent } from "laravel-vite-plugin/inertia-helpers";
import MainLayout from "./Layouts/MainLayout";

const appName = import.meta.env.VITE_APP_NAME || "Laravel";

createInertiaApp({
    title: (title) => `${title} - ${appName}`,
    resolve: (name) =>
        resolvePageComponent(
            `./Pages/${name}.jsx`,
            import.meta.glob("./Pages/**/*.jsx")
        ),
    setup({ el, App, props }) {
        const root = createRoot(el);

        root.render(
            <App
                {...props}
                initialPage={{
                    ...props.initialPage,
                    props: {
                        ...props.initialPage.props,
                        auth: props.initialPage.props.auth,
                    },
                }}
                // Wrap every page in MainLayout
                children={(page) => (
                    <MainLayout auth={props.initialPage.props.auth}>
                        {page}
                    </MainLayout>
                )}
            />
        );
    },
    progress: {
        color: "#4B5563",
    },
});
